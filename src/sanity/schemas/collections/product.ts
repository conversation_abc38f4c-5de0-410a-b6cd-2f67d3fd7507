const product = {
  name: 'product',
  title: 'Product',
  type: 'document',
  fields: [
    {
      name: 'ean',
      title: 'EAN',
      type: 'string',
    },
    {
      name: 'title',
      title: 'Title',
      type: 'string',
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'ean',
        slugify: (input: string) => `product/${input}`,
      },
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
    },
    {
      name: 'brand',
      title: 'Brand',
      type: 'string',
    },
    {
      name: 'collection',
      title: 'Collection',
      type: 'string',
    },
    {
      name: 'price',
      title: 'Price',
      type: 'number',
    },
    {
      name: 'articleGroup',
      title: 'Article Group',
      type: 'object',
      fields: [
        { name: 'name', title: 'Name', type: 'string' },
        { name: 'id', title: 'ID', type: 'number' },
      ],
    },
    {
      name: 'referenceNo',
      title: 'Reference Number',
      type: 'string',
    },
    {
      name: 'stock',
      title: 'Stock',
      type: 'number',
    },
    {
      name: 'productGroup',
      title: 'Product Group',
      type: 'string',
    },
    {
      name: 'productLine',
      title: 'Product Line',
      type: 'string',
    },
    {
      name: 'image',
      title: 'Main Image',
      type: 'image',
    },
    {
      name: 'specialData',
      title: 'Special Data',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            { name: 'name', title: 'Name', type: 'string' },
            { name: 'value', title: 'Value', type: 'string' },
          ],
        },
      ],
    },
    {
      name: 'specialAttributes',
      title: 'Special Attributes',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            { name: 'name', title: 'Name', type: 'string' },
            { name: 'value', title: 'Value', type: 'string' },
          ],
        },
      ],
    },
    {
      name: 'watchDetails',
      title: 'Watch Details',
      type: 'object',
      fields: [
        { name: 'armband', title: 'Armband', type: 'string' },
        { name: 'caseWidth', title: 'Case Width', type: 'string' },
        { name: 'caseShape', title: 'Case Shape', type: 'string' },
        { name: 'caseMaterial', title: 'Case Material', type: 'string' },
        { name: 'glassType', title: 'Glass Type', type: 'string' },
        { name: 'dial', title: 'Dial', type: 'string' },
        { name: 'dialColor', title: 'Dial Color', type: 'string' },
        { name: 'movement', title: 'Movement', type: 'string' },
        { name: 'clasp', title: 'Clasp', type: 'string' },
        { name: 'waterTight', title: 'Water Tight', type: 'number' },
      ],
    },
    {
      name: 'jewelryDetails',
      title: 'Jewelry Details',
      type: 'object',
      fields: [
        { name: 'sizeType', title: 'Size Type', type: 'number' },
        { name: 'serviceInterval', title: 'Service Interval', type: 'number' },
        {
          name: 'diamonds',
          title: 'Diamonds',
          type: 'array',
          of: [
            {
              type: 'object',
              fields: [
                { name: 'carat', title: 'Carat', type: 'number' },
                { name: 'clarity', title: 'Clarity', type: 'string' },
                { name: 'color', title: 'Color', type: 'string' },
                { name: 'cut', title: 'Cut', type: 'string' },
                { name: 'cutQuality', title: 'Cut Quality', type: 'string' },
              ],
            },
          ],
        },
      ],
    },
  ],
};

export default product;
